'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import FAQ from '../components/sections/FAQ';
import HowToUse from '../components/sections/HowToUse';
import StructuredData from '../components/seo/StructuredData';
import { useNotification } from '../components/ui/notification';
import { downloadPhonePreview } from '../utils/download';
import { Message, ThemeMode, TimeFormat } from '../types/message';
import { useTimeInput } from '../hooks/useTimeInput';

import MessageInput from '../components/ui/MessageInput';
import PhonePreview from '../components/device/PhonePreview';
import { performanceMonitor } from '../utils/performance';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { checkBrowserCompatibility } from '../utils/errorHandling';
import { accessibilityManager } from '../utils/accessibility';
// Main component
export default function FakeTextGenerator() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const phonePreviewRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<any>(null);
  const [recipientName, setRecipientName] = useState('fake text message');
  const [recipientAvatar, setRecipientAvatar] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      sender: 'recipient',
      content: 'Did you see that viral video of the cat playing piano? 😂',
      method: 'data',
      status: 'default'
    },
    {
      id: '2',
      sender: 'user',
      content: 'OMG yes! I can\'t believe it got 1 million views in one day! 🎹🐱',
      method: 'data',
      status: 'default'
    },
    {
      id: '3',
      sender: 'system',
      content: 'Message delivered',
      method: 'data',
      status: 'default'
    }
  ]);
  const {
    deviceTime,
    timeFormat,
    timeError,
    isTimeValid,
    handleFormattedTimeChange,
    handleTimeFormatChange,
    resetTime
  } = useTimeInput();
  const [mode, setMode] = useState<ThemeMode>('light');

  // 电量状态管理
  const [batteryPercentage, setBatteryPercentage] = useState<number>(85);
  const [batteryError, setBatteryError] = useState<string>('');

  // Download state management
  const [isDownloading, setIsDownloading] = useState<boolean>(false);

  // Notification system
  const notification = useNotification();

  // 电量输入处理函数
  const handleBatteryChange = useCallback((value: string) => {
    // 只允许数字输入
    const numericValue = value.replace(/[^0-9]/g, '');

    if (numericValue === '') {
      setBatteryPercentage(0);
      setBatteryError('');
      return;
    }

    const percentage = parseInt(numericValue, 10);

    // 验证范围 (0-100)
    if (percentage < 0 || percentage > 100) {
      setBatteryError('Battery percentage must be between 0 and 100');
      return;
    }

    setBatteryPercentage(percentage);
    setBatteryError('');
  }, []);

  // 检查浏览器兼容性
  useEffect(() => {
    const compatibility = checkBrowserCompatibility();
    if (!compatibility.compatible) {
      notification.toast.error(
        `Your browser may not support all features. Issues detected: ${compatibility.issues.join(', ')}. Please try using Chrome, Firefox, or Safari.`
      );
    }
  }, [notification]);

  // Download handler function
  const handleDownload = useCallback(async () => {
    if (!phonePreviewRef.current) {
      notification.toast.error('Preview component not found, please try again');
      return;
    }

    if (isDownloading) {
      return; // Prevent duplicate downloads
    }

    setIsDownloading(true);

    // Show loading toast and save its ID
    const loadingToastId = notification.toast.loading('Generating image...');

    try {
      await downloadPhonePreview(phonePreviewRef.current);

      // Close loading toast and show success
      notification.utils.hide(loadingToastId);
      notification.toast.success('Download successful!');

      // 无障碍公告
      accessibilityManager.announce('Image downloaded successfully', 'polite');

      // 在开发环境下显示性能报告
      if (process.env.NODE_ENV === 'development') {
        console.log(performanceMonitor.generateReport());
      }
    } catch (error) {
      console.error('Download failed:', error);

      // Close loading toast and show error
      notification.utils.hide(loadingToastId);
      notification.toast.error(error instanceof Error ? error.message : 'Download failed, please try again');
    } finally {
      setIsDownloading(false);
    }
  }, [isDownloading, notification]);

  const handleAvatarUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setRecipientAvatar(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const addMessage = () => {
    // This function is now handled by the new MessageInput component
    // Kept for backward compatibility but functionality moved to MessageInput
  };

  const addRecipientMessage = () => {
    // This function is now handled by the new MessageInput component
    // Kept for backward compatibility but functionality moved to MessageInput
  };

  // New function to handle messages from the enhanced MessageInput
  const handleMessagesChange = useCallback((newMessages: Message[]) => {
    setMessages(newMessages);
  }, []);

  // removeMessage function removed - now handled by MessageInput component

  const handleReset = () => {
    setRecipientName('');
    setRecipientAvatar(null);
    setMessages([]);
    resetTime(); // Use the hook's reset function
    setMode('light');
    setBatteryPercentage(85); // 重置电量为默认值85%
    setBatteryError(''); // 清除电量错误信息

    // Reset MessageInput component
    if (messageInputRef.current) {
      messageInputRef.current.resetMessages();
    }
  };

  return (
    <>
      <StructuredData />
      <div className="min-h-screen bg-gray-50">
        <Header />

        {/* Hero Section */}
        <section className="bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                Fake Text Message Generator
              </h1>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Create realistic fake text messages and SMS conversations for free. Perfect for text story videos,
                mockups, and creative projects. No email required, instant download.
              </p>
              <div className="flex flex-wrap justify-center gap-4 text-sm text-gray-500">
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  100% Free
                </span>
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  No Registration
                </span>
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  High Quality
                </span>
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Mobile Friendly
                </span>
              </div>
            </div>
          </div>
        </section>

        {/* Main content area - responsive layout */}
        <main
          id="main-content"
          className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8"
          role="main"
          aria-label="Fake text message generator"
        >
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
            {/* Left side: Control panel */}
            <div
              className="space-y-3 sm:space-y-4"
              role="region"
              aria-label="Message configuration controls"
            >
              {/* User settings combination - avatar and name */}
              <section
                className="bg-white rounded-lg p-3 sm:p-4 shadow-sm"
                aria-labelledby="user-settings-heading"
              >
                <h3
                  id="user-settings-heading"
                  className="text-base sm:text-lg font-semibold text-gray-900 mb-3"
                >
                  User Settings
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  {/* Avatar upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Recipient Avatar</label>
                    <div className="flex items-center space-x-3">
                      <div className="relative group">
                        <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-gray-200 transition-all duration-200 group-hover:border-blue-300">
                          {recipientAvatar ? (
                            <img src={recipientAvatar} alt="Avatar" className="w-full h-full object-cover" />
                          ) : (
                            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                              </svg>
                            </div>
                          )}
                        </div>
                        {recipientAvatar && (
                          <button
                            onClick={() => setRecipientAvatar(null)}
                            className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                          >
                            ×
                          </button>
                        )}
                      </div>
                      <div className="flex-1">
                        <button
                          onClick={() => fileInputRef.current?.click()}
                          className="w-full px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-all duration-200 font-medium text-sm"
                        >
                          {recipientAvatar ? 'Change' : 'Upload'}
                        </button>
                        <p className="text-xs text-gray-500 mt-1">JPG, PNG up to 5MB</p>
                      </div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarUpload}
                        className="hidden"
                      />
                    </div>
                  </div>

                  {/* Recipient name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Recipient Name</label>
                    <input
                      type="text"
                      value={recipientName}
                      onChange={(e) => setRecipientName(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                      placeholder="e.g. fake text message"
                    />
                  </div>
                </div>
              </section>

              {/* Device configuration */}
              <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm">
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3">Device Configuration</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Device Time</label>
                    <input
                      type="text"
                      value={deviceTime}
                      onChange={(e) => handleFormattedTimeChange(e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent text-black transition-colors ${
                        isTimeValid
                          ? 'border-gray-300 focus:ring-blue-500'
                          : 'border-red-300 focus:ring-red-500 bg-red-50'
                      }`}
                      placeholder={timeFormat === '12' ? 'H:MM' : 'HH:MM'}
                      maxLength={timeFormat === '12' ? 4 : 5}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Format: {timeFormat === '12' ? '12-hour (e.g. 9:41)' : '24-hour (e.g. 09:41 or 21:30)'}
                    </p>
                    {!isTimeValid && timeError && (
                      <p className="text-red-500 text-xs mt-1 flex items-center">
                        <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {timeError}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Time Format</label>
                    <select
                      value={timeFormat}
                      onChange={(e) => handleTimeFormatChange(e.target.value as TimeFormat)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                    >
                      <option value="12">12-hour</option>
                      <option value="24">24-hour</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Theme Mode</label>
                    <select
                      value={mode}
                      onChange={(e) => setMode(e.target.value as ThemeMode)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-black"
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Battery Level</label>
                    <input
                      type="text"
                      value={batteryPercentage}
                      onChange={(e) => handleBatteryChange(e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:border-transparent text-black transition-colors ${
                        batteryError
                          ? 'border-red-300 focus:ring-red-500 bg-red-50'
                          : 'border-gray-300 focus:ring-blue-500'
                      }`}
                      placeholder="85"
                      maxLength={3}
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Enter percentage (0-100)
                    </p>
                    {batteryError && (
                      <p className="text-red-500 text-xs mt-1 flex items-center">
                        <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        {batteryError}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Message management */}
              <div className="bg-white rounded-lg p-3 sm:p-4 shadow-sm">
                <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3">Message Management</h3>
                <MessageInput
                  ref={messageInputRef}
                  onAddUser={addMessage}
                  onAddRecipient={addRecipientMessage}
                  messages={messages}
                  onMessagesChange={handleMessagesChange}
                  placeholder="Type your message here..."
                  maxLength={500}
                />
              </div>


            </div>

            {/* 右侧：iPhone预览 */}
            <div
              className="flex flex-col items-center w-full order-first xl:order-last"
              role="region"
              aria-label="iPhone preview and download controls"
            >
              {/* 操作按钮 */}
              <div className="w-full max-w-sm mb-4 sm:mb-6 px-2 sm:px-4 lg:px-0">
                <div className="flex flex-col xs:flex-row space-y-2 xs:space-y-0 xs:space-x-3">
                  <button
                    onClick={async () => {
                      const confirmed = await notification.modal.confirmDanger(
                        'Are you sure you want to reset all settings? This action cannot be undone.',
                        {
                          title: 'Reset All Settings',
                          confirmText: 'Reset',
                          cancelText: 'Cancel'
                        }
                      );
                      if (confirmed) {
                        handleReset();
                      }
                    }}
                    className="flex-1 px-3 sm:px-4 py-2 sm:py-2.5 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all duration-200 font-medium text-xs sm:text-sm flex items-center justify-center"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Reset All
                  </button>
                  <button
                    onClick={handleDownload}
                    disabled={isDownloading}
                    aria-disabled={isDownloading}
                    aria-busy={isDownloading}
                    className="flex-1 px-3 sm:px-4 py-2 sm:py-2.5 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-200 font-medium text-xs sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-blue-500 pointer-events-auto disabled:pointer-events-none flex items-center justify-center"
                  >
                    {isDownloading ? (
                      <>
                        <LoadingSpinner size="sm" color="white" className="-ml-1 mr-2" />
                        Downloading...
                      </>
                    ) : (
                      <>
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Download Image
                      </>
                    )}
                  </button>
                </div>
              </div>

              {/* iPhone预览 */}
              <div
                className="xl:sticky xl:top-24"
                role="img"
                aria-label="iPhone message preview"
              >
                <PhonePreview
                  ref={phonePreviewRef}
                  recipientName={recipientName}
                  recipientAvatar={recipientAvatar}
                  messages={messages}
                  deviceTime={deviceTime}
                  timeFormat={timeFormat}
                  mode={mode}
                  batteryPercentage={batteryPercentage}
                />
                <p className="text-center text-sm text-gray-500 mt-4 font-medium">
                  <svg className="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Preview is scrollable
                </p>
              </div>
            </div>
          </div>
        </main>

        {/* How to Use Section */}
        <HowToUse />

        {/* FAQ Section */}
        <FAQ />

        <Footer />
      </div>
    </>
  );
}
