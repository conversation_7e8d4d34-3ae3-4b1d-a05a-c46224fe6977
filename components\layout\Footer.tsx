'use client';

import Link from 'next/link';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <svg 
                  className="w-5 h-5 text-white" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" 
                  />
                </svg>
              </div>
              <span className="text-xl font-semibold text-gray-900">Fake Text Generator</span>
            </div>
            <p className="text-gray-600 mb-4 max-w-md">
              Create realistic fake text messages and SMS conversations for free. Perfect for text story videos, 
              mockups, and creative projects. No email required, instant download.
            </p>
            <div className="flex space-x-4">
              <Link 
                href="https://twitter.com/faketextgen" 
                className="text-gray-400 hover:text-blue-500 transition-colors"
                aria-label="Follow us on Twitter"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </Link>
              <Link 
                href="https://github.com/faketextgen" 
                className="text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="View our GitHub repository"
                target="_blank"
                rel="noopener noreferrer"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              Quick Links
            </h3>
            <ul className="space-y-3">
              <li>
                <Link 
                  href="#generator" 
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  Text Generator
                </Link>
              </li>
              <li>
                <Link 
                  href="#how-to-use" 
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  How to Use
                </Link>
              </li>
              <li>
                <Link 
                  href="#faq" 
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  FAQ
                </Link>
              </li>
              <li>
                <Link
                  href="/about"
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  href="/changelog"
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  Updates
                </Link>
              </li>
              <li>
                <Link
                  href="/help"
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  Help Center
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              Legal
            </h3>
            <ul className="space-y-3">
              <li>
                <Link 
                  href="/privacy-policy" 
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link 
                  href="/terms-of-service" 
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link 
                  href="/contact" 
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  Contact Us
                </Link>
              </li>
              <li>
                <Link 
                  href="/disclaimer" 
                  className="text-gray-600 hover:text-blue-600 transition-colors"
                >
                  Disclaimer
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 text-sm">
              © {currentYear} Fake Text Generator. All rights reserved.
            </p>
            <div className="mt-4 md:mt-0 flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-6">
              <div className="flex items-center space-x-6">
                <span className="text-gray-500 text-sm">
                  Made with ❤️ for creators
                </span>
                <div className="flex items-center space-x-2 text-gray-500 text-sm">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  <span>Secure & Private</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row items-center space-y-3 sm:space-y-0 sm:space-x-4">
                {/* Indie.Deals Badge - 完整SVG实现，包含悬停闪光动画效果 */}
                <div className="flex-shrink-0">
                  <a
                    href="https://indie.deals?ref=https%3A%2F%2Fwww.faketextmessage.xyz%2F"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="relative inline-block overflow-hidden transition-opacity duration-300 hover:opacity-90"
                    style={{
                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                    }}
                  >
                    <svg
                      width="120"
                      height="40"
                      viewBox="0 0 120 40"
                      xmlns="http://www.w3.org/2000/svg"
                      className="indie-deals-badge"
                    >
                      <defs>
                        <linearGradient id="badgeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                          <stop offset="0%" stopColor="#ffffff" />
                          <stop offset="100%" stopColor="#e6f0fc" />
                        </linearGradient>
                      </defs>

                      <rect
                        width="120"
                        height="40"
                        rx="10"
                        fill="url(#badgeGradient)"
                      />

                      <rect
                        x="0.75"
                        y="0.75"
                        width="118.5"
                        height="38.5"
                        rx="9.25"
                        fill="none"
                        stroke="#0070f3"
                        strokeWidth="1.5"
                        strokeOpacity="0.3"
                      />

                      <image
                        href="https://indie.deals/logo_badge.png"
                        x="9.6"
                        y="8"
                        width="24"
                        height="24"
                        preserveAspectRatio="xMidYMid meet"
                        style={{ filter: 'drop-shadow(1px 1px 2px rgba(0,0,0,0.15))' }}
                      />

                      <text
                        x="80.4"
                        y="15.2"
                        textAnchor="middle"
                        dominantBaseline="middle"
                        fontFamily="system-ui, -apple-system, sans-serif"
                        fontSize="7.199999999999999"
                        fontWeight="normal"
                        fill="#4b5563"
                        letterSpacing="0.01em"
                      >
                        Find us on
                      </text>
                      <text
                        x="80.4"
                        y="26"
                        textAnchor="middle"
                        dominantBaseline="middle"
                        fontFamily="system-ui, -apple-system, sans-serif"
                        fontSize="8.8"
                        fontWeight="bold"
                        fill="#0070f3"
                        letterSpacing="0.01em"
                      >
                        Indie.Deals
                      </text>
                    </svg>
                  </a>
                </div>

                {/* Twelve Tools Badge */}
                <div className="flex-shrink-0">
                  <a href="https://twelve.tools" target="_blank" rel="noopener noreferrer">
                    <img src="https://twelve.tools/badge0-white.svg" alt="Featured on Twelve Tools" width="200" height="54" className="h-10 w-auto transition-opacity hover:opacity-80" />
                  </a>
                </div>

                {/* Starter Best Badge */}
                <div className="flex-shrink-0">
                  <a href="https://starterbest.com" target="_blank" rel="noopener noreferrer">
                    <img
                      src="https://starterbest.com/badages-awards.svg"
                      alt="Featured on Starter Best"
                      className="h-[54px] w-auto transition-opacity hover:opacity-80"
                    />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
